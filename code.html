<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enhanced Selection UI</title>
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
        position: relative;
        overflow-x: hidden;
      }

      body::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 80%,
            rgba(120, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 119, 198, 0.15) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(120, 219, 255, 0.1) 0%,
            transparent 50%
          );
        pointer-events: none;
      }

      .container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
          0 15px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
        padding: 40px;
        width: 100%;
        max-width: 520px;
        position: relative;
        z-index: 1;
        animation: slideInUp 0.6s ease-out;
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(30px) scale(0.96);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      h1 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 35px;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .selection-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 35px;
      }

      .selection-btn {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 16px;
        padding: 24px 16px;
        cursor: pointer;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        display: flex;
        flex-direction: column;
        align-items: center;
        font-weight: 600;
        color: #4a5568;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .selection-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.1) 0%,
          rgba(118, 75, 162, 0.1) 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      .selection-btn:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 25px rgba(102, 126, 234, 0.15),
          0 10px 10px rgba(0, 0, 0, 0.04);
        border-color: rgba(102, 126, 234, 0.2);
      }

      .selection-btn:hover::before {
        opacity: 1;
      }

      .selection-btn:hover i {
        transform: scale(1.1);
        color: #667eea;
      }

      .selection-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: transparent;
        transform: translateY(-4px) scale(1.05);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4),
          0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .selection-btn.active::before {
        opacity: 0;
      }

      .selection-btn i {
        font-size: 32px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
      }

      .selection-btn span {
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }

      .item-count {
        position: absolute;
        top: 8px;
        right: 8px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
      }

      .selection-btn:hover .item-count {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
      }

      .selection-btn.active .item-count {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .popup {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(8px);
        z-index: 100;
        justify-content: center;
        align-items: center;
        animation: backdropFadeIn 0.3s ease;
      }

      .popup-content {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 35px;
        width: 90%;
        max-width: 420px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25),
          0 15px 35px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
        animation: popupSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
      }

      @keyframes backdropFadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes popupSlideIn {
        from {
          opacity: 0;
          transform: translateY(30px) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      }

      .popup-header h2 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 22px;
        font-weight: 700;
        letter-spacing: -0.3px;
      }

      .close-btn {
        background: rgba(0, 0, 0, 0.04);
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #666;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
      }

      .close-btn:hover {
        background: rgba(220, 38, 38, 0.1);
        color: #dc2626;
        transform: scale(1.05);
      }

      .form-group {
        margin-bottom: 24px;
      }

      .form-group label {
        display: block;
        margin-bottom: 10px;
        font-weight: 600;
        color: #374151;
        font-size: 15px;
        letter-spacing: 0.2px;
      }

      .form-group input {
        width: 100%;
        padding: 14px 18px;
        border: 2px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
        outline: none;
      }

      .form-group input:focus {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
          0 4px 12px rgba(102, 126, 234, 0.15);
        transform: translateY(-1px);
      }

      .form-group input::placeholder {
        color: #9ca3af;
      }

      .submit-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 16px 24px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        width: 100%;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        margin-top: 10px;
      }

      .submit-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
      }

      .submit-btn:hover::before {
        opacity: 1;
      }

      .submit-btn:active {
        transform: translateY(0);
      }

      .submit-btn span {
        position: relative;
        z-index: 1;
      }

      @media (max-width: 768px) {
        .selection-buttons {
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
        }
      }

      @media (max-width: 480px) {
        .selection-buttons {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .container {
          padding: 24px;
          margin: 10px;
        }

        .popup-content {
          padding: 28px;
          margin: 20px;
        }

        .selection-btn {
          padding: 20px 16px;
        }

        .selection-btn i {
          font-size: 28px;
        }

        h1 {
          font-size: 24px;
          margin-bottom: 28px;
        }
      }

      /* Loading state for submit buttons */
      .submit-btn.loading {
        pointer-events: none;
        position: relative;
      }

      .submit-btn.loading::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        margin-left: -10px;
        margin-top: -10px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .submit-btn.loading span {
        opacity: 0;
      }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
  </head>
  <body>
    <div class="container">
      <h1>Select an Option</h1>

      <div class="selection-buttons">
        <div class="selection-btn" data-type="box">
          <div class="item-count" id="box-count-display">0</div>
          <i class="fas fa-box-open"></i>
          <span>Box</span>
        </div>

        <div class="selection-btn" data-type="lock">
          <div class="item-count" id="lock-count-display">0</div>
          <i class="fas fa-lock"></i>
          <span>Lock</span>
        </div>

        <div class="selection-btn" data-type="files">
          <div class="item-count" id="files-count-display">0</div>
          <i class="fas fa-folder-open"></i>
          <span>Files</span>
        </div>

        <div class="selection-btn" data-type="other">
          <i class="fas fa-ellipsis-h"></i>
          <span>Other</span>
        </div>
      </div>
    </div>

    <!-- Box Popup -->
    <div class="popup" id="box-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h2>Add Boxes</h2>
          <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
          <label for="box-count">Number of Boxes</label>
          <input
            type="number"
            id="box-count"
            min="1"
            placeholder="Enter number of boxes"
          />
        </div>
        <button class="submit-btn"><span>Add Boxes</span></button>
      </div>
    </div>

    <!-- Lock Popup -->
    <div class="popup" id="lock-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h2>Add Locks</h2>
          <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
          <label for="lock-start">Start ID</label>
          <input
            type="number"
            id="lock-start"
            min="1"
            placeholder="Enter start ID"
          />
        </div>
        <div class="form-group">
          <label for="lock-end">End ID</label>
          <input
            type="number"
            id="lock-end"
            min="1"
            placeholder="Enter end ID"
          />
        </div>
        <button class="submit-btn"><span>Add Locks</span></button>
      </div>
    </div>

    <!-- Files Popup -->
    <div class="popup" id="files-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h2>Add Files</h2>
          <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
          <label for="files-count">Number of Files</label>
          <input
            type="number"
            id="files-count"
            min="1"
            placeholder="Enter number of files"
          />
        </div>
        <button class="submit-btn"><span>Add Files</span></button>
      </div>
    </div>

    <!-- Other Popup -->
    <div class="popup" id="other-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h2>Add Other Item</h2>
          <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
          <label for="other-name">Item Name</label>
          <input type="text" id="other-name" placeholder="Enter item name" />
        </div>
        <div class="form-group">
          <label for="other-count">Item Count</label>
          <input
            type="number"
            id="other-count"
            min="1"
            placeholder="Enter item count"
          />
        </div>
        <button class="submit-btn"><span>Add Item</span></button>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Item counters
        let itemCounts = {
          box: 50,
          lock: 20,
          files: 800,
        };

        // Store custom types created through "Other"
        let customTypes = {};

        // Selection buttons container
        const selectionButtonsContainer =
          document.querySelector(".selection-buttons");

        // Get initial selection buttons
        let selectionButtons = document.querySelectorAll(".selection-btn");

        const popups = {
          box: document.getElementById("box-popup"),
          lock: document.getElementById("lock-popup"),
          files: document.getElementById("files-popup"),
          other: document.getElementById("other-popup"),
        };

        // Count display elements
        const countDisplays = {
          box: document.getElementById("box-count-display"),
          lock: document.getElementById("lock-count-display"),
          files: document.getElementById("files-count-display"),
        };

        // Close buttons
        const closeButtons = document.querySelectorAll(".close-btn");

        // Function to create a new type card
        function createNewTypeCard(typeName, count) {
          const typeId = typeName.toLowerCase().replace(/\s+/g, "-");

          // Create the new button element
          const newButton = document.createElement("div");
          newButton.className = "selection-btn";
          newButton.setAttribute("data-type", typeId);

          // Create the count display
          const countDisplay = document.createElement("div");
          countDisplay.className = "item-count";
          countDisplay.id = `${typeId}-count-display`;
          countDisplay.textContent = count;

          // Create the icon (using a generic icon for custom types)
          const icon = document.createElement("i");
          icon.className = "fas fa-cube";

          // Create the label
          const label = document.createElement("span");
          label.textContent = typeName;

          // Assemble the button
          newButton.appendChild(countDisplay);
          newButton.appendChild(icon);
          newButton.appendChild(label);

          return {
            button: newButton,
            typeId: typeId,
            countDisplay: countDisplay,
          };
        }

        // Function to insert new type card before "Other" button
        function insertNewTypeCard(typeName, count) {
          const { button, typeId, countDisplay } = createNewTypeCard(
            typeName,
            count
          );

          // Find the "Other" button
          const otherButton = document.querySelector('[data-type="other"]');

          // Ensure the "Other" button exists before trying to insert
          if (!otherButton) {
            console.error("Other button not found");
            return;
          }

          // Insert the new button before the "Other" button
          selectionButtonsContainer.insertBefore(button, otherButton);

          // Store the custom type data
          customTypes[typeId] = {
            name: typeName,
            count: count,
            countDisplay: countDisplay,
          };

          // Add the count display to our tracking object
          countDisplays[typeId] = countDisplay;
          itemCounts[typeId] = count;

          // Add click event listener to the new button
          button.addEventListener("click", function () {
            // Remove active class from all buttons
            document
              .querySelectorAll(".selection-btn")
              .forEach((btn) => btn.classList.remove("active"));

            // Add active class to clicked button
            this.classList.add("active");

            // Create and show a dynamic popup for this custom type
            showCustomTypePopup(typeName, typeId);
          });

          // Update the selectionButtons NodeList
          selectionButtons = document.querySelectorAll(".selection-btn");
        }

        // Function to create and show popup for custom types
        function showCustomTypePopup(typeName, typeId) {
          // Remove any existing custom popup
          const existingPopup = document.getElementById(`${typeId}-popup`);
          if (existingPopup) {
            existingPopup.remove();
          }

          // Create popup HTML
          const popupHTML = `
            <div class="popup" id="${typeId}-popup" style="display: flex;">
              <div class="popup-content">
                <div class="popup-header">
                  <h2>Add ${typeName}</h2>
                  <button class="close-btn">&times;</button>
                </div>
                <div class="form-group">
                  <label for="${typeId}-count-input">Number of ${typeName}</label>
                  <input
                    type="number"
                    id="${typeId}-count-input"
                    min="1"
                    placeholder="Enter number of ${typeName.toLowerCase()}"
                  />
                </div>
                <button class="submit-btn" data-type-id="${typeId}" data-type-name="${typeName}">
                  <span>Add ${typeName}</span>
                </button>
              </div>
            </div>
          `;

          // Add popup to body
          document.body.insertAdjacentHTML("beforeend", popupHTML);

          // Get the new popup and its elements
          const popup = document.getElementById(`${typeId}-popup`);
          const closeBtn = popup.querySelector(".close-btn");
          const submitBtn = popup.querySelector(".submit-btn");

          // Add close event listener
          closeBtn.addEventListener("click", () => {
            popup.remove();
            // Remove active class from all buttons
            document
              .querySelectorAll(".selection-btn")
              .forEach((btn) => btn.classList.remove("active"));
          });

          // Add submit event listener
          submitBtn.addEventListener("click", function () {
            const countInput = document.getElementById(`${typeId}-count-input`);
            const count = parseInt(countInput.value);

            if (!count || count <= 0) {
              showFieldError(
                `${typeId}-count-input`,
                "Please enter a valid number"
              );
              return;
            }

            // Show loading state
            showLoadingState(this);

            // Simulate API call
            setTimeout(() => {
              hideLoadingState(this);

              // Update the custom type count
              customTypes[typeId].count += count;
              customTypes[typeId].countDisplay.textContent =
                customTypes[typeId].count;
              itemCounts[typeId] = customTypes[typeId].count;

              // Add animation to highlight the update
              customTypes[typeId].countDisplay.style.transform = "scale(1.3)";
              customTypes[typeId].countDisplay.style.transition =
                "transform 0.2s ease";

              setTimeout(() => {
                customTypes[typeId].countDisplay.style.transform = "scale(1)";
              }, 200);

              showSuccessMessage(
                `Added ${count} ${typeName}${count > 1 ? "s" : ""}`
              );

              // Close popup and clear form
              popup.remove();
              document
                .querySelectorAll(".selection-btn")
                .forEach((btn) => btn.classList.remove("active"));
            }, 1500);
          });

          // Close popup when clicking outside
          popup.addEventListener("click", function (e) {
            if (e.target === popup) {
              popup.remove();
              document
                .querySelectorAll(".selection-btn")
                .forEach((btn) => btn.classList.remove("active"));
            }
          });
        }

        // Initialize count displays
        function initializeCountDisplays() {
          for (const type in itemCounts) {
            if (countDisplays[type]) {
              countDisplays[type].textContent = itemCounts[type];
            }
          }
        }

        // Update count display function
        function updateCountDisplay(type, count) {
          itemCounts[type] += count;
          countDisplays[type].textContent = itemCounts[type];

          // Add animation to highlight the update
          countDisplays[type].style.transform = "scale(1.3)";
          countDisplays[type].style.transition = "transform 0.2s ease";

          setTimeout(() => {
            countDisplays[type].style.transform = "scale(1)";
          }, 200);
        }

        // Initialize displays with current counts
        initializeCountDisplays();

        // Add click event to selection buttons
        selectionButtons.forEach((button) => {
          button.addEventListener("click", function () {
            // Remove active class from all buttons
            selectionButtons.forEach((btn) => btn.classList.remove("active"));

            // Add active class to clicked button
            this.classList.add("active");

            // Get the type from data attribute
            const type = this.getAttribute("data-type");

            // Show the corresponding popup
            showPopup(type);
          });
        });

        // Add click event to close buttons
        closeButtons.forEach((button) => {
          button.addEventListener("click", function () {
            // Hide all popups
            hideAllPopups();
          });
        });

        // Close popup when clicking outside
        document.addEventListener("click", function (e) {
          if (e.target.classList.contains("popup")) {
            hideAllPopups();
          }
        });

        // Show specific popup
        function showPopup(type) {
          // First hide all popups
          hideAllPopups();

          // Show the selected popup
          if (popups[type]) {
            popups[type].style.display = "flex";
          }
        }

        // Hide all popups
        function hideAllPopups() {
          for (const popup in popups) {
            popups[popup].style.display = "none";
          }

          // Remove active class from all buttons
          selectionButtons.forEach((btn) => btn.classList.remove("active"));
        }

        // Enhanced form submission with validation and loading states
        const submitButtons = document.querySelectorAll(".submit-btn");
        submitButtons.forEach((button) => {
          button.addEventListener("click", function () {
            const popup = this.closest(".popup");
            const popupId = popup.id;
            let isValid = true;
            let message = "";

            // Validate inputs based on popup type
            if (popupId === "box-popup") {
              const count = document.getElementById("box-count").value;
              if (!count || count <= 0) {
                isValid = false;
                showFieldError(
                  "box-count",
                  "Please enter a valid number of boxes"
                );
              } else {
                message = `Added ${count} box${count > 1 ? "es" : ""}`;
              }
            } else if (popupId === "lock-popup") {
              const start = document.getElementById("lock-start").value;
              const end = document.getElementById("lock-end").value;
              if (!start || !end || start <= 0 || end <= 0) {
                isValid = false;
                if (!start || start <= 0)
                  showFieldError("lock-start", "Please enter a valid start ID");
                if (!end || end <= 0)
                  showFieldError("lock-end", "Please enter a valid end ID");
              } else if (parseInt(start) > parseInt(end)) {
                isValid = false;
                showFieldError(
                  "lock-end",
                  "End ID must be greater than or equal to start ID"
                );
              } else {
                message = `Added locks from ID ${start} to ${end}`;
              }
            } else if (popupId === "files-popup") {
              const count = document.getElementById("files-count").value;
              if (!count || count <= 0) {
                isValid = false;
                showFieldError(
                  "files-count",
                  "Please enter a valid number of files"
                );
              } else {
                message = `Added ${count} file${count > 1 ? "s" : ""}`;
              }
            } else if (popupId === "other-popup") {
              const name = document.getElementById("other-name").value;
              const count = document.getElementById("other-count").value;
              if (!name.trim()) {
                isValid = false;
                showFieldError("other-name", "Please enter an item name");
              }
              if (!count || count <= 0) {
                isValid = false;
                showFieldError("other-count", "Please enter a valid count");
              }
              if (isValid) {
                // Check if this type already exists
                const typeId = name.toLowerCase().replace(/\s+/g, "-");
                if (customTypes[typeId]) {
                  // If type exists, just update the count
                  const existingCount = customTypes[typeId].count;
                  const newCount = parseInt(count);
                  customTypes[typeId].count = existingCount + newCount;
                  customTypes[typeId].countDisplay.textContent =
                    customTypes[typeId].count;
                  itemCounts[typeId] = customTypes[typeId].count;

                  // Add animation to highlight the update
                  customTypes[typeId].countDisplay.style.transform =
                    "scale(1.3)";
                  customTypes[typeId].countDisplay.style.transition =
                    "transform 0.2s ease";

                  setTimeout(() => {
                    customTypes[typeId].countDisplay.style.transform =
                      "scale(1)";
                  }, 200);

                  message = `Added ${count} more ${name}${
                    count > 1 ? "s" : ""
                  } (Total: ${customTypes[typeId].count})`;
                } else {
                  message = `Created new type: ${name} with ${count} item${
                    count > 1 ? "s" : ""
                  }`;
                }
              }
            }

            if (isValid) {
              // Show loading state
              showLoadingState(button);

              // Simulate API call
              setTimeout(() => {
                hideLoadingState(button);
                showSuccessMessage(message);

                // Update item counts
                if (popupId === "box-popup") {
                  const count = parseInt(
                    document.getElementById("box-count").value
                  );
                  updateCountDisplay("box", count);
                } else if (popupId === "lock-popup") {
                  const start = parseInt(
                    document.getElementById("lock-start").value
                  );
                  const end = parseInt(
                    document.getElementById("lock-end").value
                  );
                  const lockCount = end - start + 1;
                  updateCountDisplay("lock", lockCount);
                } else if (popupId === "files-popup") {
                  const count = parseInt(
                    document.getElementById("files-count").value
                  );
                  updateCountDisplay("files", count);
                } else if (popupId === "other-popup") {
                  // Handle custom type creation
                  const name = document.getElementById("other-name").value;
                  const count = parseInt(
                    document.getElementById("other-count").value
                  );
                  const typeId = name.toLowerCase().replace(/\s+/g, "-");

                  // Only create new card if this is a new type
                  if (!customTypes[typeId]) {
                    insertNewTypeCard(name, count);
                  }
                  // If type already exists, the count was already updated in the validation section
                }

                clearFormInputs(popup);
                hideAllPopups();
              }, 1500);
            }
          });
        });

        // Utility functions for enhanced UX
        function showFieldError(fieldId, message) {
          const field = document.getElementById(fieldId);
          field.style.borderColor = "#dc2626";
          field.style.boxShadow = "0 0 0 3px rgba(220, 38, 38, 0.1)";

          // Remove existing error message
          const existingError =
            field.parentNode.querySelector(".error-message");
          if (existingError) existingError.remove();

          // Add error message
          const errorDiv = document.createElement("div");
          errorDiv.className = "error-message";
          errorDiv.style.color = "#dc2626";
          errorDiv.style.fontSize = "14px";
          errorDiv.style.marginTop = "5px";
          errorDiv.textContent = message;
          field.parentNode.appendChild(errorDiv);

          // Clear error on input
          field.addEventListener(
            "input",
            function () {
              this.style.borderColor = "";
              this.style.boxShadow = "";
              const errorMsg = this.parentNode.querySelector(".error-message");
              if (errorMsg) errorMsg.remove();
            },
            { once: true }
          );
        }

        function showLoadingState(button) {
          button.classList.add("loading");
          button.disabled = true;
        }

        function hideLoadingState(button) {
          button.classList.remove("loading");
          button.disabled = false;
        }

        function showSuccessMessage(message) {
          // Create success notification
          const notification = document.createElement("div");
          notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    padding: 16px 24px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
                    z-index: 200;
                    animation: slideInRight 0.3s ease;
                    font-weight: 600;
                `;
          notification.textContent = message;
          document.body.appendChild(notification);

          // Add slide in animation
          const style = document.createElement("style");
          style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                `;
          document.head.appendChild(style);

          // Remove notification after 3 seconds
          setTimeout(() => {
            notification.style.animation = "slideInRight 0.3s ease reverse";
            setTimeout(() => notification.remove(), 300);
          }, 3000);
        }

        function clearFormInputs(popup) {
          const inputs = popup.querySelectorAll("input");
          inputs.forEach((input) => {
            input.value = "";
            input.style.borderColor = "";
            input.style.boxShadow = "";
          });

          // Remove any error messages
          const errorMessages = popup.querySelectorAll(".error-message");
          errorMessages.forEach((msg) => msg.remove());
        }
      });
    </script>
  </body>
</html>
